<h1 align="center">
  MHC Voice Recommendator Backend
</h1>

<h4 align="center">Backend service for a voice-based AI recommendation system</h4>
<br>

# Prerequisites

- To develop:
  - [Node.js](https://nodejs.org/) (v18 or higher)
  - [Google Cloud SDK](https://cloud.google.com/sdk/docs/install)
  - [Visual Studio Code](https://code.visualstudio.com/)
  - [Yarn](https://classic.yarnpkg.com/lang/en/docs/install/)
  - [Prettier Formatter for VS Code](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode). Formatting rules at `.prettierrc.cjs`

<br>

# How To Use for Development

To clone and run this application, make sure you have all the [prerequisites](#prerequisites) installed on your computer.

From your command line:

```bash
# Clone this repository
$ git clone [repository-url]

# Go into the repository
$ cd recomendador-voz-backend

# Install dependencies
$ yarn install

# Setup .env file

# Run the server in development mode
$ yarn dev
```

<br>

# Build and Deploy

To deploy this application to Google Cloud Run, from your command line:

```bash
# Build and deploy to Google Cloud Run
$ yarn deploy
```

<br>

# Google Cloud Speech-to-Text Configuration

This application uses Google Cloud Speech-to-Text API for voice recognition. To enable speech recognition, you need to configure Google Cloud credentials.

## Option 1: Service Account Key File (Recommended for Development)

1. Create a service account in Google Cloud Console
2. Download the service account key JSON file
3. Set the environment variable in your `.env` file:
```
GOOGLE_APPLICATION_CREDENTIALS="/path/to/your/service-account-key.json"
```

## Option 2: Base64 Encoded Key (Recommended for Production)

1. Encode your service account key file to base64:
```bash
base64 -i service-account-key.json
```
2. Set the environment variable in your `.env` file:
```
GOOGLE_SERVICE_ACCOUNT_KEY_BASE64="your_base64_encoded_service_account_key"
```

## Option 3: Individual Credentials

Set these environment variables in your `.env` file:
```
GOOGLE_CLOUD_PROJECT_ID="your-project-id"
GOOGLE_CLOUD_CLIENT_EMAIL="<EMAIL>"
GOOGLE_CLOUD_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour private key here\n-----END PRIVATE KEY-----"
```

## Fallback Mode

If Google Cloud credentials are not configured, the application will run in fallback mode with speech recognition disabled. The application will log clear messages about the missing configuration and continue to work for other features.

<br>

# System Architecture

The system consists of the following main components:

- **WebSocket Server**: Handles real-time communication with clients using Socket.IO
- **Speech-to-Text Service**: Uses Google's Speech-to-Text API to convert audio input to text
- **AI**: Uses Google's Vertex AI with Gemini 2.0 Flash model to process text queries
- **Text-to-Speech Service**: Uses Speech Tools Server to convert AI responses to speech
