const express = require('express')
const { createServer } = require('http')
const { Server } = require('socket.io')
const cors = require('cors')
const hpp = require('hpp')
const { SpeechClient } = require('@google-cloud/speech')
const config = require('./modules/config')
const { createAIService, generateAIReply } = require('./modules/AIService')
const { generateSpeech } = require('./modules/voiceService')
const { customLog } = require('./modules/logger')
const { cleanTextForSpeech } = require('./modules/textProcessor')
const { setSecurityHeaders, validateApiKey, configureSocketSecurity, configureCors } = require('./modules/security')
const cookieParser = require('cookie-parser')
const csurf = require('csurf')
const csrfProtection = csurf({ cookie: true })

require('better-logging')(console)

const app = express()
app.disable('x-powered-by')
const httpServer = createServer(app)

setSecurityHeaders(app)

app.use(hpp())
app.use(express.json())
configureCors(app)
app.use(cookieParser())
app.use(csrfProtection)

app.use('/api', validateApiKey)

const io = new Server(httpServer, {
  cors: {
    origin:
      process.env.NODE_ENV === 'production'
        ? [process.env.FRONTEND_DEV_URL, process.env.FRONTEND_PRO_URL]
        : ['http://localhost:3000', 'http://localhost:5173'],
    methods: ['GET', 'POST'],
    credentials: true
  }
})
configureSocketSecurity(io)

createAIService()

// Initialize Google Cloud Speech client with proper error handling
let client = null
let speechRecognitionAvailable = false

try {
  if (config.HAS_GOOGLE_CLOUD_CREDENTIALS) {
    client = new SpeechClient(config.GOOGLE_CLOUD_CONFIG)
    speechRecognitionAvailable = true
    customLog('✅ Google Cloud Speech client initialized successfully')
  } else {
    customLog('⚠️ Google Cloud credentials not found. Speech recognition will be disabled.')
    customLog('💡 To enable speech recognition, please configure Google Cloud credentials in your .env file')
  }
} catch (error) {
  customLog('❌ Failed to initialize Google Cloud Speech client:', {
    message: error.message,
    code: error.code || 'NO_CODE'
  })
  speechRecognitionAvailable = false
}

io.on('connection', (socket) => {
  customLog('Client connected:', socket.id)

  // Notify client about speech recognition availability
  socket.emit('service_status', {
    speechRecognition: speechRecognitionAvailable,
    message: speechRecognitionAvailable
      ? 'Speech recognition is available'
      : 'Speech recognition is disabled - missing Google Cloud credentials'
  })

  let recognizeStream = null
  let conversationHistory = []
  let isMuted = false
  let isStreamActive = true
  let inactivityTimeout = null
  let retryCount = 0
  let maxRetries = 3
  let retryDelay = 1000 // Start with 1 second delay

  const startRecognitionStream = () => {
    // Check if speech recognition is available
    if (!speechRecognitionAvailable || !client) {
      customLog('❌ Speech recognition not available - missing Google Cloud credentials')
      socket.emit('error', {
        type: 'service_unavailable',
        message: 'Speech recognition service is not configured. Please contact support.'
      })
      return null
    }

    if (recognizeStream && !recognizeStream.destroyed) {
      recognizeStream.end()
    }

    customLog('Starting new recognition stream')
    isStreamActive = true

    return client
      .streamingRecognize(config.SPEECH_TO_TEXT_CONFIG)
      .on('error', (error) => {
        // Enhanced error logging with more details
        customLog('Error transcribing audio:', {
          message: error.message || 'Unknown error',
          code: error.code || 'NO_CODE',
          details: error.details || 'No details available',
          stack: error.stack || 'No stack trace',
          timestamp: new Date().toISOString()
        })

        // Check for specific Google Cloud authentication errors
        if (error.code === 16 || error.message?.includes('authentication') || error.message?.includes('credentials')) {
          customLog('❌ Google Cloud authentication error detected. Please check your credentials configuration.')
          socket.emit('error', {
            type: 'authentication',
            message: 'Speech recognition service authentication failed. Please check server configuration.'
          })
          retryCount = maxRetries // Don't retry authentication errors
        } else if (error.code === 3 || error.message?.includes('INVALID_ARGUMENT')) {
          customLog('❌ Invalid configuration for Google Cloud Speech API')
          socket.emit('error', {
            type: 'configuration',
            message: 'Speech recognition configuration error.'
          })
          retryCount = maxRetries // Don't retry configuration errors
        } else {
          customLog('❌ General speech recognition error:', error.message || 'Unknown error')

          // Implement retry logic for transient errors
          if (retryCount < maxRetries) {
            retryCount++
            customLog(`🔄 Attempting to restart recognition stream (retry ${retryCount}/${maxRetries}) in ${retryDelay}ms`)

            setTimeout(() => {
              if (speechRecognitionAvailable && client) {
                recognizeStream = startRecognitionStream()
                if (recognizeStream) {
                  customLog('✅ Recognition stream restarted successfully')
                  retryDelay = Math.min(retryDelay * 2, 10000) // Exponential backoff, max 10 seconds
                }
              }
            }, retryDelay)
          } else {
            customLog('❌ Max retries reached, disabling speech recognition for this session')
            socket.emit('error', {
              type: 'max_retries',
              message: 'Speech recognition temporarily unavailable after multiple attempts.'
            })
          }
        }

        isStreamActive = false

        if (recognizeStream) {
          recognizeStream.end()
          recognizeStream = null
        }
      })
      .on('data', async (data) => {
        if (data.results[0] && data.results[0].alternatives[0]) {
          // Reset retry count on successful data reception
          retryCount = 0
          retryDelay = 1000 // Reset delay to initial value

          resetInactivityTimer()

          let transcription = data.results[0].alternatives[0].transcript.trim()
          socket.emit('transcription', transcription)

          if (data.results[0].isFinal && transcription && transcription !== '') {
            socket.emit('loading')
            if (transcription.toLowerCase() === 'ok aura') {
              socket.emit('reply', { text: '[OK AURA]', audioUrl: null })
            } else {
              if (transcription.toLowerCase().startsWith('ok aura')) {
                transcription = transcription.slice(7).trim()
              }

              if (transcription.length > config.MAX_USER_INPUT_LENGTH) {
                customLog(`Warning: Truncating long user input from ${transcription.length} to ${config.MAX_USER_INPUT_LENGTH} characters`)
                transcription = transcription.substring(0, config.MAX_USER_INPUT_LENGTH)
              }

              conversationHistory.push({ user: true, content: transcription })
              customLog(`User: ${transcription}`)
              let reply = await generateAIReply(conversationHistory)
              customLog(`AI: ${reply}`)
              conversationHistory.push({ user: false, content: reply })
              if (
                reply.startsWith('[JSON]') ||
                reply.startsWith('[NONE]') ||
                reply.startsWith('[COMMAND]') ||
                reply.startsWith('[PAUSE]') ||
                reply.startsWith('[EXIT]')
              ) {
                socket.emit('reply', { text: reply, audioUrl: null })
              } else {
                const audioUrl = !isMuted
                  ? `data:audio/mpeg;base64,${(await generateSpeech(cleanTextForSpeech(reply))).toString('base64')}`
                  : null
                socket.emit('reply', { text: reply, audioUrl })
              }
            }
          }
        }
      })
  }

  const resetInactivityTimer = () => {
    if (inactivityTimeout) {
      clearTimeout(inactivityTimeout)
    }

    inactivityTimeout = setTimeout(() => {
      if (recognizeStream && !recognizeStream.destroyed) {
        customLog('Closing stream due to inactivity')
        recognizeStream.end()
        recognizeStream = null
        isStreamActive = false
      }
    }, 60000) // 1 minute
  }

  socket.on('audio', (audioBuffer) => {
    if (!speechRecognitionAvailable) {
      // Silently ignore audio data if speech recognition is not available
      return
    }

    if (!recognizeStream || !isStreamActive) {
      recognizeStream = startRecognitionStream()

      // If startRecognitionStream returns null, speech recognition is not available
      if (!recognizeStream) {
        return
      }
    }

    const buffer = Buffer.from(new Uint8Array(audioBuffer))

    try {
      recognizeStream.write(buffer)
      resetInactivityTimer()
    } catch (error) {
      customLog('Error writing to stream:', {
        message: error.message || 'Unknown write error',
        code: error.code || 'NO_CODE',
        timestamp: new Date().toISOString()
      })

      // Attempt to restart the stream
      try {
        recognizeStream = startRecognitionStream()
        recognizeStream.write(buffer)
        customLog('Successfully restarted recognition stream after write error')
      } catch (restartError) {
        customLog('Failed to restart recognition stream:', {
          message: restartError.message || 'Unknown restart error',
          code: restartError.code || 'NO_CODE'
        })
        socket.emit('error', {
          type: 'stream_restart',
          message: 'Unable to restart speech recognition stream.'
        })
      }
    }
  })

  socket.on('disconnect', () => {
    customLog('Client disconnected:', socket.id)
    if (recognizeStream) {
      recognizeStream.end()
      recognizeStream = null
    }
    if (inactivityTimeout) {
      clearTimeout(inactivityTimeout)
    }
  })

  socket.on('mute-sound', () => {
    isMuted = true
    customLog(`Sound muted for client: ${socket.id}`)
  })

  socket.on('unmute-sound', () => {
    isMuted = false
    customLog(`Sound unmuted for client: ${socket.id}`)
  })

  // Only start recognition stream if speech recognition is available
  if (speechRecognitionAvailable) {
    recognizeStream = startRecognitionStream()
    resetInactivityTimer()
  } else {
    customLog('⚠️ Skipping recognition stream initialization - speech recognition not available')
  }
})

httpServer.listen(config.PORT, () => {
  customLog(`Server running on port ${config.PORT}`)
})
